# LangChain + LangServe SSE完整架构指南

> 基于LangChain和LangServe构建企业级流式处理系统的完整解决方案

## 🎯 架构概览

```
前端 (JavaScript) → LangServe (FastAPI) → LangChain (流式处理) → AI API
     ↓                    ↓                    ↓              ↓
EventSource连接      自动SSE转换        Generator对象      原始流式响应
```

## 📋 技术栈

### 后端技术栈
- **LangChain 0.3+**: 核心AI框架
- **LangServe**: 官方Web服务框架
- **FastAPI**: 底层Web框架
- **Uvicorn**: ASGI服务器

### 前端技术栈
- **EventSource API**: 浏览器原生SSE支持
- **JavaScript/TypeScript**: 前端逻辑
- **任意前端框架**: React/Vue/Angular等

## 🏗️ 后端架构实现

### 1. 项目结构
```
project/
├── app/
│   ├── __init__.py
│   ├── main.py              # FastAPI应用入口
│   ├── chains/              # LangChain链定义
│   │   ├── __init__.py
│   │   ├── chat_chain.py    # 聊天链
│   │   └── rag_chain.py     # RAG链
│   ├── models/              # 数据模型
│   │   ├── __init__.py
│   │   └── schemas.py       # Pydantic模型
│   └── config/              # 配置文件
│       ├── __init__.py
│       └── settings.py      # 应用配置
├── requirements.txt
└── README.md
```

### 2. 核心实现代码

#### main.py - 应用入口
```python
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from langserve import add_routes
from app.chains.chat_chain import create_chat_chain
from app.chains.rag_chain import create_rag_chain

app = FastAPI(
    title="LangChain SSE API",
    version="1.0.0",
    description="基于LangChain和LangServe的流式处理API"
)

# CORS配置
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 生产环境请限制域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 创建LangChain链
chat_chain = create_chat_chain()
rag_chain = create_rag_chain()

# 添加LangServe路由 - 自动提供SSE端点
add_routes(
    app,
    chat_chain,
    path="/chat",
    enabled_endpoints=["invoke", "stream", "batch"],  # 启用流式端点
)

add_routes(
    app,
    rag_chain,
    path="/rag",
    enabled_endpoints=["invoke", "stream", "batch"],
)

# 健康检查端点
@app.get("/health")
async def health_check():
    return {"status": "healthy"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
```

#### chains/chat_chain.py - 聊天链
```python
from langchain_openai import ChatOpenAI
from langchain_core.prompts import ChatPromptTemplate, MessagesPlaceholder
from langchain_core.output_parsers import StrOutputParser
from langchain_core.runnables import RunnablePassthrough
from app.models.schemas import ChatInput

def create_chat_chain():
    """创建聊天链"""
    
    # 初始化LLM - 必须启用streaming
    llm = ChatOpenAI(
        model="gpt-4",
        temperature=0.7,
        streaming=True,  # 关键：启用流式处理
        max_tokens=1000
    )
    
    # 创建提示模板
    prompt = ChatPromptTemplate.from_messages([
        ("system", "你是一个有用的AI助手。请用中文回答用户的问题。"),
        MessagesPlaceholder(variable_name="chat_history"),
        ("human", "{question}")
    ])
    
    # 构建链
    chain = (
        RunnablePassthrough.assign(
            chat_history=lambda x: x.get("chat_history", [])
        )
        | prompt
        | llm
        | StrOutputParser()
    )
    
    # 添加输入类型验证
    return chain.with_types(input_type=ChatInput)
```

#### chains/rag_chain.py - RAG链
```python
from langchain_openai import ChatOpenAI, OpenAIEmbeddings
from langchain_chroma import Chroma
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.output_parsers import StrOutputParser
from langchain_core.runnables import RunnableParallel, RunnablePassthrough
from app.models.schemas import RAGInput

def create_rag_chain():
    """创建RAG链"""
    
    # 初始化组件
    llm = ChatOpenAI(
        model="gpt-4",
        temperature=0.1,
        streaming=True,  # 启用流式处理
    )
    
    embeddings = OpenAIEmbeddings()
    
    # 初始化向量数据库（示例）
    vectorstore = Chroma(
        embedding_function=embeddings,
        persist_directory="./chroma_db"
    )
    retriever = vectorstore.as_retriever(search_kwargs={"k": 4})
    
    # RAG提示模板
    prompt = ChatPromptTemplate.from_template("""
    基于以下上下文信息回答问题。如果上下文中没有相关信息，请说明无法回答。

    上下文：
    {context}

    问题：{question}

    回答：
    """)
    
    # 文档格式化函数
    def format_docs(docs):
        return "\n\n".join(doc.page_content for doc in docs)
    
    # 构建RAG链
    rag_chain = (
        RunnableParallel({
            "context": retriever | format_docs,
            "question": RunnablePassthrough()
        })
        | prompt
        | llm
        | StrOutputParser()
    )
    
    return rag_chain.with_types(input_type=RAGInput)
```

#### models/schemas.py - 数据模型
```python
from pydantic import BaseModel, Field
from typing import List, Optional, Union
from langchain_core.messages import HumanMessage, AIMessage, SystemMessage

class ChatInput(BaseModel):
    """聊天输入模型"""
    question: str = Field(..., description="用户问题")
    chat_history: Optional[List[Union[HumanMessage, AIMessage, SystemMessage]]] = Field(
        default=[], description="聊天历史"
    )

class RAGInput(BaseModel):
    """RAG输入模型"""
    question: str = Field(..., description="用户问题")
    
class ChatResponse(BaseModel):
    """聊天响应模型"""
    answer: str = Field(..., description="AI回答")
    
class StreamChunk(BaseModel):
    """流式响应块"""
    content: str = Field(..., description="内容块")
    finished: bool = Field(default=False, description="是否完成")
```

## 🌐 前端架构实现

### 1. 核心SSE客户端类

#### sse-client.js - SSE客户端封装
```javascript
class LangServeSSEClient {
    constructor(baseUrl = 'http://localhost:8000') {
        this.baseUrl = baseUrl;
        this.eventSource = null;
    }

    /**
     * 流式聊天
     * @param {Object} params - 参数
     * @param {string} params.question - 问题
     * @param {Array} params.chat_history - 聊天历史
     * @param {Function} onMessage - 消息回调
     * @param {Function} onError - 错误回调
     * @param {Function} onComplete - 完成回调
     */
    async streamChat({ question, chat_history = [] }, onMessage, onError, onComplete) {
        const url = `${this.baseUrl}/chat/stream`;
        
        try {
            const response = await fetch(url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    input: { question, chat_history }
                })
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const reader = response.body.getReader();
            const decoder = new TextDecoder();
            let buffer = '';

            while (true) {
                const { done, value } = await reader.read();
                
                if (done) {
                    onComplete && onComplete();
                    break;
                }

                buffer += decoder.decode(value, { stream: true });
                const lines = buffer.split('\n');
                buffer = lines.pop(); // 保留不完整的行

                for (const line of lines) {
                    if (line.startsWith('data: ')) {
                        const data = line.slice(6);
                        if (data.trim() === '[DONE]') {
                            onComplete && onComplete();
                            return;
                        }
                        
                        try {
                            const parsed = JSON.parse(data);
                            onMessage && onMessage(parsed);
                        } catch (e) {
                            // 处理非JSON数据（纯文本流）
                            onMessage && onMessage({ content: data });
                        }
                    }
                }
            }
        } catch (error) {
            onError && onError(error);
        }
    }

    /**
     * 流式RAG查询
     */
    async streamRAG({ question }, onMessage, onError, onComplete) {
        const url = `${this.baseUrl}/rag/stream`;
        
        return this.streamChat({ question }, onMessage, onError, onComplete);
    }

    /**
     * 停止当前流
     */
    stop() {
        if (this.eventSource) {
            this.eventSource.close();
            this.eventSource = null;
        }
    }
}

export default LangServeSSEClient;
```

### 2. React组件示例

#### ChatComponent.jsx - React聊天组件
```jsx
import React, { useState, useRef, useEffect } from 'react';
import LangServeSSEClient from './sse-client';

const ChatComponent = () => {
    const [messages, setMessages] = useState([]);
    const [input, setInput] = useState('');
    const [isStreaming, setIsStreaming] = useState(false);
    const [currentResponse, setCurrentResponse] = useState('');
    
    const clientRef = useRef(new LangServeSSEClient());
    const messagesEndRef = useRef(null);

    const scrollToBottom = () => {
        messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
    };

    useEffect(() => {
        scrollToBottom();
    }, [messages, currentResponse]);

    const handleSend = async () => {
        if (!input.trim() || isStreaming) return;

        const userMessage = { role: 'user', content: input };
        setMessages(prev => [...prev, userMessage]);
        setInput('');
        setIsStreaming(true);
        setCurrentResponse('');

        const client = clientRef.current;
        
        await client.streamChat(
            {
                question: input,
                chat_history: messages
            },
            // onMessage
            (chunk) => {
                if (chunk.content) {
                    setCurrentResponse(prev => prev + chunk.content);
                }
            },
            // onError
            (error) => {
                console.error('Streaming error:', error);
                setCurrentResponse('抱歉，发生了错误。');
                setIsStreaming(false);
            },
            // onComplete
            () => {
                setMessages(prev => [...prev, { 
                    role: 'assistant', 
                    content: currentResponse 
                }]);
                setCurrentResponse('');
                setIsStreaming(false);
            }
        );
    };

    const handleKeyPress = (e) => {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            handleSend();
        }
    };

    return (
        <div className="chat-container">
            <div className="messages">
                {messages.map((msg, index) => (
                    <div key={index} className={`message ${msg.role}`}>
                        <div className="content">{msg.content}</div>
                    </div>
                ))}
                
                {isStreaming && currentResponse && (
                    <div className="message assistant streaming">
                        <div className="content">
                            {currentResponse}
                            <span className="cursor">|</span>
                        </div>
                    </div>
                )}
                
                <div ref={messagesEndRef} />
            </div>
            
            <div className="input-area">
                <textarea
                    value={input}
                    onChange={(e) => setInput(e.target.value)}
                    onKeyPress={handleKeyPress}
                    placeholder="输入您的问题..."
                    disabled={isStreaming}
                />
                <button 
                    onClick={handleSend} 
                    disabled={isStreaming || !input.trim()}
                >
                    {isStreaming ? '发送中...' : '发送'}
                </button>
            </div>
        </div>
    );
};

export default ChatComponent;
```

### 3. Vue组件示例

#### ChatComponent.vue - Vue聊天组件
```vue
<template>
  <div class="chat-container">
    <div class="messages" ref="messagesContainer">
      <div 
        v-for="(msg, index) in messages" 
        :key="index" 
        :class="['message', msg.role]"
      >
        <div class="content">{{ msg.content }}</div>
      </div>
      
      <div v-if="isStreaming && currentResponse" class="message assistant streaming">
        <div class="content">
          {{ currentResponse }}
          <span class="cursor">|</span>
        </div>
      </div>
    </div>
    
    <div class="input-area">
      <textarea
        v-model="input"
        @keypress="handleKeyPress"
        placeholder="输入您的问题..."
        :disabled="isStreaming"
      />
      <button 
        @click="handleSend" 
        :disabled="isStreaming || !input.trim()"
      >
        {{ isStreaming ? '发送中...' : '发送' }}
      </button>
    </div>
  </div>
</template>

<script>
import LangServeSSEClient from './sse-client';

export default {
  name: 'ChatComponent',
  data() {
    return {
      messages: [],
      input: '',
      isStreaming: false,
      currentResponse: '',
      client: new LangServeSSEClient()
    };
  },
  methods: {
    async handleSend() {
      if (!this.input.trim() || this.isStreaming) return;

      const userMessage = { role: 'user', content: this.input };
      this.messages.push(userMessage);
      const question = this.input;
      this.input = '';
      this.isStreaming = true;
      this.currentResponse = '';

      await this.client.streamChat(
        {
          question,
          chat_history: this.messages
        },
        // onMessage
        (chunk) => {
          if (chunk.content) {
            this.currentResponse += chunk.content;
          }
        },
        // onError
        (error) => {
          console.error('Streaming error:', error);
          this.currentResponse = '抱歉，发生了错误。';
          this.isStreaming = false;
        },
        // onComplete
        () => {
          this.messages.push({ 
            role: 'assistant', 
            content: this.currentResponse 
          });
          this.currentResponse = '';
          this.isStreaming = false;
          this.$nextTick(() => {
            this.scrollToBottom();
          });
        }
      );
    },

    handleKeyPress(e) {
      if (e.key === 'Enter' && !e.shiftKey) {
        e.preventDefault();
        this.handleSend();
      }
    },

    scrollToBottom() {
      const container = this.$refs.messagesContainer;
      container.scrollTop = container.scrollHeight;
    }
  },

  watch: {
    messages: {
      handler() {
        this.$nextTick(() => {
          this.scrollToBottom();
        });
      },
      deep: true
    }
  }
};
</script>
```

## 🚀 部署配置

### 1. requirements.txt
```txt
fastapi>=0.104.0
langchain>=0.1.0
langserve>=0.0.40
langchain-openai>=0.0.5
langchain-community>=0.0.20
langchain-core>=0.1.0
uvicorn[standard]>=0.24.0
python-dotenv>=1.0.0
```

### 2. Docker配置

#### Dockerfile
```dockerfile
FROM python:3.11-slim

WORKDIR /app

COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

COPY . .

EXPOSE 8000

CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000"]
```

#### docker-compose.yml
```yaml
version: '3.8'

services:
  langserve-api:
    build: .
    ports:
      - "8000:8000"
    environment:
      - OPENAI_API_KEY=${OPENAI_API_KEY}
    volumes:
      - ./chroma_db:/app/chroma_db
    restart: unless-stopped
```

## 📝 使用说明

### 1. 启动后端服务
```bash
# 安装依赖
pip install -r requirements.txt

# 设置环境变量
export OPENAI_API_KEY="your-api-key"

# 启动服务
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

### 2. 前端集成
```javascript
// 初始化客户端
const client = new LangServeSSEClient('http://localhost:8000');

// 流式聊天
await client.streamChat(
    { question: "你好，请介绍一下自己" },
    (chunk) => console.log('收到:', chunk.content),
    (error) => console.error('错误:', error),
    () => console.log('完成')
);
```

### 3. API端点
- `POST /chat/invoke` - 同步聊天
- `POST /chat/stream` - 流式聊天（SSE）
- `POST /rag/invoke` - 同步RAG查询
- `POST /rag/stream` - 流式RAG查询（SSE）

## 🔧 高级配置

### 1. 自定义SSE格式
```python
from langserve import add_routes
from langserve.serialization import WellKnownLCSerializer

# 自定义序列化器
serializer = WellKnownLCSerializer()

add_routes(
    app,
    chain,
    path="/custom",
    input_type=CustomInput,
    output_type=str,
    serializer=serializer,
)
```

### 2. 错误处理
```python
from fastapi import HTTPException
from langserve.server import _InvokeRequest

@app.exception_handler(Exception)
async def global_exception_handler(request, exc):
    return {"error": str(exc), "type": type(exc).__name__}
```

## 🛠️ 实用工具函数

### 1. 通用SSE工具类

#### utils/sse-utils.js - 通用SSE工具
```javascript
/**
 * SSE连接管理器
 */
class SSEManager {
    constructor() {
        this.connections = new Map();
    }

    /**
     * 创建SSE连接
     */
    createConnection(id, url, options = {}) {
        if (this.connections.has(id)) {
            this.closeConnection(id);
        }

        const eventSource = new EventSource(url);

        eventSource.onopen = () => {
            console.log(`SSE连接已建立: ${id}`);
            options.onOpen && options.onOpen();
        };

        eventSource.onmessage = (event) => {
            try {
                const data = JSON.parse(event.data);
                options.onMessage && options.onMessage(data);
            } catch (e) {
                options.onMessage && options.onMessage({ content: event.data });
            }
        };

        eventSource.onerror = (error) => {
            console.error(`SSE连接错误: ${id}`, error);
            options.onError && options.onError(error);
        };

        this.connections.set(id, eventSource);
        return eventSource;
    }

    /**
     * 关闭连接
     */
    closeConnection(id) {
        const connection = this.connections.get(id);
        if (connection) {
            connection.close();
            this.connections.delete(id);
        }
    }

    /**
     * 关闭所有连接
     */
    closeAllConnections() {
        for (const [id, connection] of this.connections) {
            connection.close();
        }
        this.connections.clear();
    }
}

export default SSEManager;
```

### 2. 错误处理和重连机制

#### utils/retry-sse.js - 重连机制
```javascript
class RetrySSEClient {
    constructor(baseUrl, options = {}) {
        this.baseUrl = baseUrl;
        this.maxRetries = options.maxRetries || 3;
        this.retryDelay = options.retryDelay || 1000;
        this.retryCount = 0;
    }

    async streamWithRetry(endpoint, payload, callbacks) {
        try {
            await this.stream(endpoint, payload, callbacks);
            this.retryCount = 0; // 重置重试计数
        } catch (error) {
            if (this.retryCount < this.maxRetries) {
                this.retryCount++;
                console.log(`重试第 ${this.retryCount} 次...`);

                await new Promise(resolve =>
                    setTimeout(resolve, this.retryDelay * this.retryCount)
                );

                return this.streamWithRetry(endpoint, payload, callbacks);
            } else {
                callbacks.onError && callbacks.onError(error);
                throw error;
            }
        }
    }

    async stream(endpoint, payload, callbacks) {
        const response = await fetch(`${this.baseUrl}${endpoint}`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ input: payload })
        });

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const reader = response.body.getReader();
        const decoder = new TextDecoder();
        let buffer = '';

        try {
            while (true) {
                const { done, value } = await reader.read();

                if (done) break;

                buffer += decoder.decode(value, { stream: true });
                const lines = buffer.split('\n');
                buffer = lines.pop();

                for (const line of lines) {
                    if (line.startsWith('data: ')) {
                        const data = line.slice(6);
                        if (data.trim() === '[DONE]') {
                            callbacks.onComplete && callbacks.onComplete();
                            return;
                        }

                        try {
                            const parsed = JSON.parse(data);
                            callbacks.onMessage && callbacks.onMessage(parsed);
                        } catch (e) {
                            callbacks.onMessage && callbacks.onMessage({ content: data });
                        }
                    }
                }
            }
        } finally {
            reader.releaseLock();
        }

        callbacks.onComplete && callbacks.onComplete();
    }
}

export default RetrySSEClient;
```

## 🎨 样式和UI组件

### 1. CSS样式

#### styles/chat.css - 聊天界面样式
```css
.chat-container {
    display: flex;
    flex-direction: column;
    height: 100vh;
    max-width: 800px;
    margin: 0 auto;
    border: 1px solid #e1e5e9;
    border-radius: 8px;
    overflow: hidden;
}

.messages {
    flex: 1;
    overflow-y: auto;
    padding: 20px;
    background-color: #f8f9fa;
}

.message {
    margin-bottom: 16px;
    display: flex;
}

.message.user {
    justify-content: flex-end;
}

.message.assistant {
    justify-content: flex-start;
}

.message .content {
    max-width: 70%;
    padding: 12px 16px;
    border-radius: 18px;
    word-wrap: break-word;
    white-space: pre-wrap;
}

.message.user .content {
    background-color: #007bff;
    color: white;
}

.message.assistant .content {
    background-color: white;
    border: 1px solid #e1e5e9;
    color: #333;
}

.message.streaming .content {
    border-color: #007bff;
    animation: pulse 1.5s infinite;
}

.cursor {
    animation: blink 1s infinite;
    color: #007bff;
}

@keyframes blink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0; }
}

@keyframes pulse {
    0% { border-color: #007bff; }
    50% { border-color: #80bdff; }
    100% { border-color: #007bff; }
}

.input-area {
    display: flex;
    padding: 20px;
    background-color: white;
    border-top: 1px solid #e1e5e9;
}

.input-area textarea {
    flex: 1;
    border: 1px solid #e1e5e9;
    border-radius: 20px;
    padding: 12px 16px;
    resize: none;
    outline: none;
    font-family: inherit;
    font-size: 14px;
    line-height: 1.4;
    max-height: 120px;
}

.input-area textarea:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.input-area button {
    margin-left: 12px;
    padding: 12px 24px;
    background-color: #007bff;
    color: white;
    border: none;
    border-radius: 20px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: background-color 0.2s;
}

.input-area button:hover:not(:disabled) {
    background-color: #0056b3;
}

.input-area button:disabled {
    background-color: #6c757d;
    cursor: not-allowed;
}

/* 加载动画 */
.loading-dots {
    display: inline-block;
}

.loading-dots::after {
    content: '';
    animation: dots 1.5s infinite;
}

@keyframes dots {
    0%, 20% { content: '.'; }
    40% { content: '..'; }
    60%, 100% { content: '...'; }
}
```

## 🧪 测试和调试

### 1. 单元测试

#### tests/test_chains.py - 链测试
```python
import pytest
from app.chains.chat_chain import create_chat_chain
from app.chains.rag_chain import create_rag_chain

@pytest.fixture
def chat_chain():
    return create_chat_chain()

@pytest.fixture
def rag_chain():
    return create_rag_chain()

def test_chat_chain_invoke(chat_chain):
    """测试聊天链同步调用"""
    result = chat_chain.invoke({
        "question": "你好",
        "chat_history": []
    })
    assert isinstance(result, str)
    assert len(result) > 0

def test_chat_chain_stream(chat_chain):
    """测试聊天链流式调用"""
    chunks = []
    for chunk in chat_chain.stream({
        "question": "请简单介绍一下自己",
        "chat_history": []
    }):
        chunks.append(chunk)

    assert len(chunks) > 0
    full_response = "".join(chunks)
    assert len(full_response) > 0

@pytest.mark.asyncio
async def test_chat_chain_astream(chat_chain):
    """测试聊天链异步流式调用"""
    chunks = []
    async for chunk in chat_chain.astream({
        "question": "你好",
        "chat_history": []
    }):
        chunks.append(chunk)

    assert len(chunks) > 0
```

### 2. 前端测试

#### tests/sse-client.test.js - SSE客户端测试
```javascript
import { describe, it, expect, vi, beforeEach } from 'vitest';
import LangServeSSEClient from '../src/sse-client';

// Mock fetch
global.fetch = vi.fn();

describe('LangServeSSEClient', () => {
    let client;

    beforeEach(() => {
        client = new LangServeSSEClient('http://localhost:8000');
        vi.clearAllMocks();
    });

    it('should create client with correct base URL', () => {
        expect(client.baseUrl).toBe('http://localhost:8000');
    });

    it('should handle streaming chat', async () => {
        const mockResponse = {
            ok: true,
            body: {
                getReader: () => ({
                    read: vi.fn()
                        .mockResolvedValueOnce({
                            done: false,
                            value: new TextEncoder().encode('data: {"content": "Hello"}\n\n')
                        })
                        .mockResolvedValueOnce({
                            done: true,
                            value: undefined
                        })
                })
            }
        };

        fetch.mockResolvedValue(mockResponse);

        const onMessage = vi.fn();
        const onComplete = vi.fn();

        await client.streamChat(
            { question: 'Hello' },
            onMessage,
            null,
            onComplete
        );

        expect(fetch).toHaveBeenCalledWith(
            'http://localhost:8000/chat/stream',
            expect.objectContaining({
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    input: { question: 'Hello', chat_history: [] }
                })
            })
        );

        expect(onMessage).toHaveBeenCalledWith({ content: 'Hello' });
        expect(onComplete).toHaveBeenCalled();
    });
});
```

## 📊 性能监控

### 1. 后端监控

#### middleware/monitoring.py - 性能监控中间件
```python
import time
import logging
from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware

logger = logging.getLogger(__name__)

class MonitoringMiddleware(BaseHTTPMiddleware):
    async def dispatch(self, request: Request, call_next):
        start_time = time.time()

        # 记录请求信息
        logger.info(f"Request: {request.method} {request.url}")

        response = await call_next(request)

        # 计算处理时间
        process_time = time.time() - start_time
        response.headers["X-Process-Time"] = str(process_time)

        # 记录响应信息
        logger.info(f"Response: {response.status_code} - {process_time:.4f}s")

        return response
```

### 2. 前端性能监控

#### utils/performance.js - 前端性能监控
```javascript
class PerformanceMonitor {
    constructor() {
        this.metrics = {
            streamingLatency: [],
            tokenRate: [],
            errorRate: 0,
            totalRequests: 0
        };
    }

    startStreaming() {
        return performance.now();
    }

    recordFirstToken(startTime) {
        const latency = performance.now() - startTime;
        this.metrics.streamingLatency.push(latency);
        console.log(`首个token延迟: ${latency.toFixed(2)}ms`);
    }

    recordTokenRate(tokenCount, duration) {
        const rate = tokenCount / (duration / 1000);
        this.metrics.tokenRate.push(rate);
        console.log(`Token速率: ${rate.toFixed(2)} tokens/s`);
    }

    recordError() {
        this.metrics.errorRate++;
        this.metrics.totalRequests++;
    }

    recordSuccess() {
        this.metrics.totalRequests++;
    }

    getMetrics() {
        const avgLatency = this.metrics.streamingLatency.reduce((a, b) => a + b, 0) / this.metrics.streamingLatency.length;
        const avgTokenRate = this.metrics.tokenRate.reduce((a, b) => a + b, 0) / this.metrics.tokenRate.length;
        const errorRate = (this.metrics.errorRate / this.metrics.totalRequests) * 100;

        return {
            averageLatency: avgLatency || 0,
            averageTokenRate: avgTokenRate || 0,
            errorRate: errorRate || 0,
            totalRequests: this.metrics.totalRequests
        };
    }
}

export default PerformanceMonitor;
```

## 🔒 安全配置

### 1. 认证中间件

#### middleware/auth.py - 认证中间件
```python
from fastapi import HTTPException, Depends, status
from fastapi.security import HTTPBearer, HTTPAuthorizationCredentials
import jwt
from typing import Optional

security = HTTPBearer()

async def verify_token(credentials: HTTPAuthorizationCredentials = Depends(security)) -> dict:
    """验证JWT token"""
    try:
        payload = jwt.decode(
            credentials.credentials,
            "your-secret-key",  # 使用环境变量
            algorithms=["HS256"]
        )
        return payload
    except jwt.ExpiredSignatureError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Token已过期"
        )
    except jwt.JWTError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="无效的token"
        )

# 在路由中使用
from langserve import add_routes

add_routes(
    app,
    chat_chain,
    path="/chat",
    dependencies=[Depends(verify_token)]  # 添加认证依赖
)
```

### 2. 前端认证

#### utils/auth.js - 前端认证
```javascript
class AuthManager {
    constructor() {
        this.token = localStorage.getItem('auth_token');
    }

    setToken(token) {
        this.token = token;
        localStorage.setItem('auth_token', token);
    }

    getAuthHeaders() {
        return this.token ? {
            'Authorization': `Bearer ${this.token}`
        } : {};
    }

    isAuthenticated() {
        return !!this.token;
    }

    logout() {
        this.token = null;
        localStorage.removeItem('auth_token');
    }
}

// 在SSE客户端中使用
class AuthenticatedSSEClient extends LangServeSSEClient {
    constructor(baseUrl, authManager) {
        super(baseUrl);
        this.authManager = authManager;
    }

    async streamChat(params, onMessage, onError, onComplete) {
        if (!this.authManager.isAuthenticated()) {
            onError && onError(new Error('未认证'));
            return;
        }

        // 添加认证头
        const headers = {
            'Content-Type': 'application/json',
            ...this.authManager.getAuthHeaders()
        };

        // 重写fetch调用以包含认证头
        // ... 实现细节
    }
}

export { AuthManager, AuthenticatedSSEClient };
```

这个完整的架构指南提供了从基础实现到高级功能的全面解决方案，您可以根据具体需求选择相应的组件进行集成。

# LangChain向量数据库集成详解

> LangChain为RAG提供的向量数据库支持和依赖管理

## 🎯 核心问题解答

### 1. ChromaDB依赖问题

#### LangChain提供的是什么？
```python
from langchain_chroma import Chroma  # LangChain的Chroma集成包装器
```

**重要**：LangChain只提供**包装器**，底层仍需要安装ChromaDB！

#### 完整的依赖安装
```bash
# 方式1：分别安装
pip install langchain-chroma  # LangChain的包装器
pip install chromadb         # ChromaDB本体

# 方式2：一次性安装（推荐）
pip install langchain-chroma[chromadb]

# 方式3：通过LangChain安装
pip install langchain[chroma]
```

#### 实际使用示例
```python
# 导入LangChain的包装器
from langchain_chroma import Chroma
from langchain_openai import OpenAIEmbeddings

# 底层仍然是ChromaDB，但通过LangChain接口使用
embeddings = OpenAIEmbeddings()
vectorstore = Chroma(
    collection_name="my_collection",
    embedding_function=embeddings,
    persist_directory="./chroma_db"  # ChromaDB的存储目录
)

# LangChain提供统一接口
docs = vectorstore.similarity_search("查询内容", k=4)
```

### 2. Milvus集成

#### 依赖安装
```bash
# Milvus集成
pip install langchain-milvus
pip install pymilvus  # Milvus Python客户端

# 或者一次性安装
pip install langchain[milvus]
```

#### 使用示例
```python
from langchain_milvus import Milvus
from langchain_openai import OpenAIEmbeddings

# 连接Milvus服务
vectorstore = Milvus(
    embedding_function=OpenAIEmbeddings(),
    connection_args={
        "host": "localhost",
        "port": "19530",
        "user": "",
        "password": "",
    },
    collection_name="langchain_collection",
)

# 统一的LangChain接口
vectorstore.add_documents(documents)
results = vectorstore.similarity_search("查询", k=5)
```

## 📊 LangChain支持的向量数据库

### 主流向量数据库支持

#### 1. **本地/轻量级**
```python
# Chroma - 最受欢迎的本地向量库
from langchain_chroma import Chroma
pip install langchain-chroma chromadb

# FAISS - Facebook开源，性能优秀
from langchain_community.vectorstores import FAISS
pip install faiss-cpu  # 或 faiss-gpu

# Qdrant - 开源向量搜索引擎
from langchain_qdrant import QdrantVectorStore
pip install langchain-qdrant qdrant-client
```

#### 2. **云端托管**
```python
# Pinecone - 最受欢迎的云端向量库
from langchain_pinecone import PineconeVectorStore
pip install langchain-pinecone pinecone-client

# Weaviate - 开源+云端
from langchain_weaviate import WeaviateVectorStore
pip install langchain-weaviate weaviate-client

# Supabase Vector - PostgreSQL扩展
from langchain_community.vectorstores import SupabaseVectorStore
pip install vecs supabase
```

#### 3. **企业级/分布式**
```python
# Milvus - 开源分布式向量数据库
from langchain_milvus import Milvus
pip install langchain-milvus pymilvus

# Elasticsearch - 支持向量搜索
from langchain_elasticsearch import ElasticsearchStore
pip install langchain-elasticsearch elasticsearch

# Redis - 支持向量搜索
from langchain_community.vectorstores import Redis
pip install redis
```

### 完整对比表

| 向量库 | LangChain包 | 底层依赖 | 适用场景 |
|--------|-------------|----------|----------|
| **Chroma** | `langchain-chroma` | `chromadb` | 本地开发、原型 |
| **FAISS** | `langchain-community` | `faiss-cpu/gpu` | 高性能本地 |
| **Pinecone** | `langchain-pinecone` | `pinecone-client` | 云端生产 |
| **Milvus** | `langchain-milvus` | `pymilvus` | 企业级分布式 |
| **Qdrant** | `langchain-qdrant` | `qdrant-client` | 开源云端 |
| **Weaviate** | `langchain-weaviate` | `weaviate-client` | GraphQL接口 |

## 🔧 LangChain为RAG提供的完整生态

### 1. **文档处理**
```python
# 文档加载器
from langchain_community.document_loaders import (
    PyPDFLoader,           # PDF文档
    TextLoader,            # 文本文件
    CSVLoader,             # CSV文件
    UnstructuredHTMLLoader, # HTML文档
    GitbookLoader,         # Gitbook
    NotionDirectoryLoader, # Notion
    WebBaseLoader,         # 网页
)

# 文档分割器
from langchain_text_splitters import (
    RecursiveCharacterTextSplitter,  # 递归字符分割
    TokenTextSplitter,               # Token分割
    MarkdownHeaderTextSplitter,      # Markdown分割
    PythonCodeTextSplitter,          # 代码分割
)
```

### 2. **Embedding模型**
```python
# OpenAI Embeddings
from langchain_openai import OpenAIEmbeddings

# 开源Embeddings
from langchain_community.embeddings import (
    HuggingFaceEmbeddings,      # Hugging Face模型
    SentenceTransformerEmbeddings, # Sentence Transformers
    OllamaEmbeddings,           # Ollama本地模型
)

# 国产Embeddings
from langchain_community.embeddings import (
    BaichuanTextEmbeddings,     # 百川
    DashScopeEmbeddings,        # 阿里云
    QianfanEmbeddingsEndpoint,  # 百度千帆
)
```

### 3. **检索器**
```python
# 基础检索器
from langchain_core.retrievers import BaseRetriever

# 高级检索器
from langchain.retrievers import (
    MultiQueryRetriever,        # 多查询检索
    ContextualCompressionRetriever, # 上下文压缩
    EnsembleRetriever,          # 集成检索
    ParentDocumentRetriever,    # 父文档检索
    SelfQueryRetriever,         # 自查询检索
)
```

### 4. **RAG链构建**
```python
from langchain_core.runnables import RunnablePassthrough
from langchain_core.output_parsers import StrOutputParser

# 标准RAG链
def create_rag_chain(vectorstore, llm):
    retriever = vectorstore.as_retriever()
    
    prompt = ChatPromptTemplate.from_template("""
    基于以下上下文回答问题：
    
    {context}
    
    问题：{question}
    """)
    
    def format_docs(docs):
        return "\n\n".join(doc.page_content for doc in docs)
    
    rag_chain = (
        {"context": retriever | format_docs, "question": RunnablePassthrough()}
        | prompt
        | llm
        | StrOutputParser()
    )
    
    return rag_chain
```

## 💡 实际项目配置示例

### 项目1：本地开发环境（Chroma）
```bash
# requirements.txt
langchain==0.1.0
langchain-chroma==0.1.0
langchain-openai==0.1.0
chromadb==0.4.0
```

```python
# config.py
from langchain_chroma import Chroma
from langchain_openai import OpenAIEmbeddings, ChatOpenAI

def setup_local_rag():
    # Embedding模型
    embeddings = OpenAIEmbeddings()
    
    # 向量数据库
    vectorstore = Chroma(
        persist_directory="./data/chroma_db",
        embedding_function=embeddings
    )
    
    # LLM
    llm = ChatOpenAI(model="gpt-3.5-turbo")
    
    return vectorstore, llm
```

### 项目2：生产环境（Pinecone）
```bash
# requirements.txt
langchain==0.1.0
langchain-pinecone==0.1.0
langchain-openai==0.1.0
pinecone-client==3.0.0
```

```python
# config.py
import os
from langchain_pinecone import PineconeVectorStore
from langchain_openai import OpenAIEmbeddings, ChatOpenAI

def setup_production_rag():
    # Embedding模型
    embeddings = OpenAIEmbeddings()
    
    # Pinecone向量数据库
    vectorstore = PineconeVectorStore(
        index_name="rag-index",
        embedding=embeddings,
        pinecone_api_key=os.getenv("PINECONE_API_KEY")
    )
    
    # LLM
    llm = ChatOpenAI(model="gpt-4")
    
    return vectorstore, llm
```

### 项目3：企业级环境（Milvus）
```bash
# requirements.txt
langchain==0.1.0
langchain-milvus==0.1.0
langchain-openai==0.1.0
pymilvus==2.3.0
```

```python
# config.py
from langchain_milvus import Milvus
from langchain_openai import OpenAIEmbeddings, ChatOpenAI

def setup_enterprise_rag():
    # Embedding模型
    embeddings = OpenAIEmbeddings()
    
    # Milvus向量数据库
    vectorstore = Milvus(
        embedding_function=embeddings,
        connection_args={
            "host": "milvus-server.company.com",
            "port": "19530",
            "user": "username",
            "password": "password",
        },
        collection_name="enterprise_docs",
        drop_old=False
    )
    
    # LLM
    llm = ChatOpenAI(model="gpt-4")
    
    return vectorstore, llm
```

## 🎯 总结

### LangChain为RAG提供的价值

1. **统一接口**：不同向量数据库使用相同的API
2. **简化集成**：减少学习和集成成本
3. **丰富生态**：文档处理、检索、生成的完整工具链
4. **灵活切换**：可以轻松切换不同的向量数据库

### 依赖管理原则

- **LangChain包**：提供统一接口和集成
- **底层依赖**：仍需安装具体的数据库客户端
- **推荐做法**：使用LangChain的集成包，享受统一API的便利

### 选择建议

- **开发阶段**：Chroma（轻量、易用）
- **生产环境**：Pinecone（托管）或Milvus（自建）
- **企业级**：Milvus、Elasticsearch（分布式、高可用）

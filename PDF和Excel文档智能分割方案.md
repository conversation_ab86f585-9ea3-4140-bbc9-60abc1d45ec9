# PDF和Excel文档智能分割方案

> 针对PDF和Excel等复杂格式文档的语义分割解决方案

## 📄 PDF文档处理挑战与解决方案

### PDF文档的特殊挑战

#### **常见问题**
1. **布局复杂**：多栏、表格、图片混排
2. **文本提取困难**：扫描PDF、图片文字
3. **格式丢失**：标题、段落结构不明确
4. **乱序问题**：文本提取顺序错乱

#### **PDF处理的完整流程**

```python
from langchain_community.document_loaders import (
    PyPDFLoader,           # 基础PDF加载
    UnstructuredPDFLoader, # 高级PDF解析
    PDFMinerLoader,        # 精确文本提取
    PDFPlumberLoader       # 表格友好
)
from langchain_openai import ChatOpenAI
import fitz  # PyMuPDF
from PIL import Image
import pytesseract  # OCR
import pandas as pd
import json
import io
import re
from typing import List, Dict, Any, Tuple

class IntelligentPDFSplitter:
    def __init__(self, llm=None):
        self.llm = llm or ChatOpenAI(model="gpt-4")
    
    def process_pdf(self, pdf_path: str) -> List[Dict[str, Any]]:
        """智能处理PDF文档"""
        
        # 1. 分析PDF类型
        pdf_type = self._analyze_pdf_type(pdf_path)
        
        # 2. 根据类型选择处理策略
        if pdf_type == "scanned":
            return self._process_scanned_pdf(pdf_path)
        elif pdf_type == "table_heavy":
            return self._process_table_pdf(pdf_path)
        elif pdf_type == "multi_column":
            return self._process_multi_column_pdf(pdf_path)
        else:
            return self._process_standard_pdf(pdf_path)
    
    def _analyze_pdf_type(self, pdf_path: str) -> str:
        """分析PDF类型"""
        doc = fitz.open(pdf_path)
        
        # 检查是否为扫描PDF
        text_ratio = self._calculate_text_ratio(doc)
        if text_ratio < 0.1:
            return "scanned"
        
        # 检查表格密度
        table_ratio = self._calculate_table_ratio(doc)
        if table_ratio > 0.3:
            return "table_heavy"
        
        # 检查是否多栏布局
        if self._is_multi_column(doc):
            return "multi_column"
        
        return "standard"
    
    def _process_scanned_pdf(self, pdf_path: str) -> List[Dict[str, Any]]:
        """处理扫描PDF（需要OCR）"""
        doc = fitz.open(pdf_path)
        chunks = []
        
        for page_num in range(len(doc)):
            page = doc[page_num]
            
            # 转换为图片
            pix = page.get_pixmap()
            img_data = pix.tobytes("png")
            
            # OCR识别
            image = Image.open(io.BytesIO(img_data))
            text = pytesseract.image_to_string(image, lang='chi_sim+eng')
            
            if text.strip():
                # 对OCR结果进行语义分割
                page_chunks = self._llm_semantic_split(text, f"第{page_num+1}页")
                chunks.extend(page_chunks)
        
        return chunks
    
    def _process_table_pdf(self, pdf_path: str) -> List[Dict[str, Any]]:
        """处理表格密集的PDF"""
        # 使用PDFPlumber处理表格
        loader = PDFPlumberLoader(pdf_path)
        documents = loader.load()
        
        chunks = []
        for doc in documents:
            # 分离文本和表格
            text_content, tables = self._extract_text_and_tables(doc.page_content)
            
            # 处理文本部分
            if text_content:
                text_chunks = self._llm_semantic_split(text_content, "文本内容")
                chunks.extend(text_chunks)
            
            # 处理表格部分
            for i, table in enumerate(tables):
                table_chunk = self._process_table_content(table, f"表格{i+1}")
                chunks.append(table_chunk)
        
        return chunks
    
    def _process_multi_column_pdf(self, pdf_path: str) -> List[Dict[str, Any]]:
        """处理多栏PDF"""
        doc = fitz.open(pdf_path)
        chunks = []
        
        for page_num in range(len(doc)):
            page = doc[page_num]
            
            # 检测栏目
            columns = self._detect_columns(page)
            
            for col_num, column_text in enumerate(columns):
                if column_text.strip():
                    col_chunks = self._llm_semantic_split(
                        column_text, 
                        f"第{page_num+1}页第{col_num+1}栏"
                    )
                    chunks.extend(col_chunks)
        
        return chunks
    
    def _process_standard_pdf(self, pdf_path: str) -> List[Dict[str, Any]]:
        """处理标准PDF"""
        # 尝试多种加载器，选择最佳结果
        loaders = [
            UnstructuredPDFLoader(pdf_path),
            PyPDFLoader(pdf_path),
            PDFMinerLoader(pdf_path)
        ]
        
        best_content = ""
        best_score = 0
        
        for loader in loaders:
            try:
                docs = loader.load()
                content = "\n".join([doc.page_content for doc in docs])
                score = self._evaluate_extraction_quality(content)
                
                if score > best_score:
                    best_content = content
                    best_score = score
            except:
                continue
        
        # 对最佳提取结果进行语义分割
        return self._llm_semantic_split(best_content, "PDF文档")
    
    def _llm_semantic_split(self, text: str, source: str) -> List[Dict[str, Any]]:
        """使用LLM进行语义分割"""
        prompt = f"""
        请对以下{source}内容进行语义分割，保持每个主题的完整性：
        
        {text}
        
        返回JSON格式：
        {{
            "chunks": [
                {{"content": "块1内容", "topic": "主题1", "type": "text"}},
                {{"content": "块2内容", "topic": "主题2", "type": "text"}}
            ]
        }}
        """
        
        try:
            response = self.llm.invoke(prompt)
            result = json.loads(response.content)
            chunks = result.get("chunks", [])
            
            # 添加源信息
            for chunk in chunks:
                chunk["source"] = source
                chunk["source_type"] = "pdf"
            
            return chunks
        except:
            # 回退到简单分割
            return [{
                "content": text,
                "topic": "未知主题",
                "type": "text",
                "source": source,
                "source_type": "pdf"
            }]
    
    def _extract_text_and_tables(self, content: str) -> Tuple[str, List[str]]:
        """分离文本和表格内容"""
        # 简化实现：识别表格模式
        lines = content.split('\n')
        text_lines = []
        table_lines = []
        current_table = []
        in_table = False
        
        for line in lines:
            # 简单的表格识别：包含多个制表符或竖线
            if '\t' in line or '|' in line or re.search(r'\s{3,}', line):
                in_table = True
                current_table.append(line)
            else:
                if in_table and current_table:
                    table_lines.append('\n'.join(current_table))
                    current_table = []
                    in_table = False
                text_lines.append(line)
        
        # 处理最后一个表格
        if current_table:
            table_lines.append('\n'.join(current_table))
        
        return '\n'.join(text_lines), table_lines
    
    def _process_table_content(self, table_text: str, table_name: str) -> Dict[str, Any]:
        """处理表格内容"""
        return {
            "content": table_text,
            "topic": table_name,
            "type": "table",
            "source": table_name,
            "source_type": "pdf_table"
        }
```

## 📊 Excel文档处理方案

### Excel文档的特殊挑战

#### **复杂性分析**
1. **多工作表**：不同主题分散在多个sheet
2. **表格结构**：行列关系、合并单元格
3. **数据类型**：数值、公式、图表
4. **业务逻辑**：财务报表、数据分析表

#### **Excel智能分割实现**

```python
import pandas as pd
import openpyxl
from typing import List, Dict, Any

class IntelligentExcelSplitter:
    def __init__(self, llm=None):
        self.llm = llm or ChatOpenAI(model="gpt-4")
    
    def process_excel(self, excel_path: str) -> List[Dict[str, Any]]:
        """智能处理Excel文档"""
        
        # 1. 分析Excel结构
        workbook_info = self._analyze_excel_structure(excel_path)
        
        # 2. 按工作表处理
        all_chunks = []
        for sheet_name, sheet_info in workbook_info.items():
            sheet_chunks = self._process_sheet(excel_path, sheet_name, sheet_info)
            all_chunks.extend(sheet_chunks)
        
        return all_chunks
    
    def _analyze_excel_structure(self, excel_path: str) -> Dict[str, Dict]:
        """分析Excel结构"""
        workbook = openpyxl.load_workbook(excel_path)
        structure = {}
        
        for sheet_name in workbook.sheetnames:
            sheet = workbook[sheet_name]
            
            # 分析工作表特征
            structure[sheet_name] = {
                "type": self._classify_sheet_type(sheet),
                "data_range": self._find_data_range(sheet),
                "has_headers": self._has_headers(sheet),
                "table_count": self._count_tables(sheet)
            }
        
        return structure
    
    def _process_sheet(self, excel_path: str, sheet_name: str, sheet_info: Dict) -> List[Dict[str, Any]]:
        """处理单个工作表"""
        
        sheet_type = sheet_info["type"]
        
        if sheet_type == "financial_report":
            return self._process_financial_sheet(excel_path, sheet_name)
        elif sheet_type == "data_table":
            return self._process_data_table_sheet(excel_path, sheet_name)
        elif sheet_type == "dashboard":
            return self._process_dashboard_sheet(excel_path, sheet_name)
        else:
            return self._process_general_sheet(excel_path, sheet_name)
    
    def _process_financial_sheet(self, excel_path: str, sheet_name: str) -> List[Dict[str, Any]]:
        """处理财务报表"""
        df = pd.read_excel(excel_path, sheet_name=sheet_name)
        
        # 识别财务报表结构
        sections = self._identify_financial_sections(df)
        
        chunks = []
        for section_name, section_data in sections.items():
            # 将表格数据转换为文本描述
            text_description = self._table_to_text(section_data, section_name)
            
            # LLM语义分割
            section_chunks = self._llm_semantic_split(
                text_description, 
                f"{sheet_name}-{section_name}"
            )
            chunks.extend(section_chunks)
        
        return chunks
    
    def _process_data_table_sheet(self, excel_path: str, sheet_name: str) -> List[Dict[str, Any]]:
        """处理数据表"""
        df = pd.read_excel(excel_path, sheet_name=sheet_name)
        
        # 按逻辑分组分割数据
        if len(df) > 100:  # 大表格需要分割
            chunks = self._split_large_table(df, sheet_name)
        else:
            # 小表格整体处理
            text_description = self._table_to_text(df, sheet_name)
            chunks = self._llm_semantic_split(text_description, sheet_name)
        
        return chunks
    
    def _split_large_table(self, df: pd.DataFrame, sheet_name: str) -> List[Dict[str, Any]]:
        """分割大型数据表"""
        chunks = []
        
        # 策略1：按行分组（每50行一组）
        chunk_size = 50
        for i in range(0, len(df), chunk_size):
            chunk_df = df.iloc[i:i+chunk_size]
            text_description = self._table_to_text(chunk_df, f"{sheet_name}_第{i//chunk_size+1}组")
            
            chunk_chunks = self._llm_semantic_split(text_description, f"{sheet_name}_数据组{i//chunk_size+1}")
            chunks.extend(chunk_chunks)
        
        return chunks
    
    def _table_to_text(self, df: pd.DataFrame, table_name: str) -> str:
        """将表格转换为文本描述"""
        
        # 生成表格摘要
        summary = f"表格名称：{table_name}\n"
        summary += f"数据行数：{len(df)}\n"
        summary += f"列数：{len(df.columns)}\n"
        summary += f"列名：{', '.join(df.columns.tolist())}\n\n"
        
        # 数据统计
        if len(df) > 0:
            summary += "数据概览：\n"
            for col in df.columns:
                if df[col].dtype in ['int64', 'float64']:
                    summary += f"- {col}: 平均值 {df[col].mean():.2f}, 最大值 {df[col].max()}, 最小值 {df[col].min()}\n"
                else:
                    summary += f"- {col}: 唯一值数量 {df[col].nunique()}\n"
        
        # 添加前几行数据作为示例
        summary += "\n数据示例：\n"
        summary += df.head(5).to_string(index=False)
        
        return summary
    
    def _identify_financial_sections(self, df: pd.DataFrame) -> Dict[str, pd.DataFrame]:
        """识别财务报表的不同部分"""
        sections = {}
        
        # 简化实现：基于关键词识别
        keywords_mapping = {
            "资产负债": ["资产", "负债", "所有者权益"],
            "利润表": ["营业收入", "营业成本", "净利润"],
            "现金流": ["经营活动", "投资活动", "筹资活动"]
        }
        
        for section_name, keywords in keywords_mapping.items():
            # 查找包含关键词的行
            mask = df.astype(str).apply(
                lambda x: x.str.contains('|'.join(keywords), na=False)
            ).any(axis=1)
            
            if mask.any():
                sections[section_name] = df[mask]
        
        # 如果没有识别出特定部分，整体作为一个部分
        if not sections:
            sections["整体数据"] = df
        
        return sections
    
    def _classify_sheet_type(self, sheet) -> str:
        """分类工作表类型"""
        # 读取前几行内容进行分析
        content = []
        for row in sheet.iter_rows(max_row=10, values_only=True):
            content.extend([str(cell) for cell in row if cell is not None])
        
        content_text = ' '.join(content).lower()
        
        # 基于关键词分类
        if any(keyword in content_text for keyword in ['资产', '负债', '利润', '收入']):
            return "financial_report"
        elif any(keyword in content_text for keyword in ['图表', '仪表板', '总览']):
            return "dashboard"
        elif len([cell for row in sheet.iter_rows(values_only=True) for cell in row if cell is not None]) > 100:
            return "data_table"
        else:
            return "general"
```

## 🔧 统一处理框架

### 多格式文档统一处理

```python
class UniversalDocumentSplitter:
    def __init__(self, llm=None):
        self.llm = llm or ChatOpenAI(model="gpt-4")
        self.pdf_splitter = IntelligentPDFSplitter(llm)
        self.excel_splitter = IntelligentExcelSplitter(llm)
    
    def process_document(self, file_path: str) -> List[Dict[str, Any]]:
        """统一处理各种格式的文档"""
        
        file_extension = file_path.lower().split('.')[-1]
        
        if file_extension == 'pdf':
            return self.pdf_splitter.process_pdf(file_path)
        elif file_extension in ['xlsx', 'xls']:
            return self.excel_splitter.process_excel(file_path)
        elif file_extension in ['docx', 'doc']:
            return self._process_word_document(file_path)
        elif file_extension == 'pptx':
            return self._process_powerpoint(file_path)
        else:
            # 尝试作为文本文件处理
            return self._process_text_file(file_path)
    
    def batch_process_documents(self, file_paths: List[str]) -> Dict[str, List[Dict[str, Any]]]:
        """批量处理多个文档"""
        results = {}
        
        for file_path in file_paths:
            try:
                chunks = self.process_document(file_path)
                results[file_path] = chunks
            except Exception as e:
                print(f"处理文件 {file_path} 失败: {e}")
                results[file_path] = []
        
        return results

# 使用示例
universal_splitter = UniversalDocumentSplitter()

# 处理单个文档
pdf_chunks = universal_splitter.process_document("财务报告.pdf")
excel_chunks = universal_splitter.process_document("数据分析.xlsx")

# 批量处理
file_list = ["报告1.pdf", "数据1.xlsx", "文档1.docx"]
all_results = universal_splitter.batch_process_documents(file_list)
```

## 💡 实际应用建议

### 针对不同文档类型的策略

1. **PDF文档**：
   - 扫描PDF → OCR + LLM分割
   - 表格PDF → 表格提取 + 文本分割
   - 标准PDF → 多加载器 + 最佳结果

2. **Excel文档**：
   - 财务报表 → 按业务逻辑分割
   - 数据表 → 按行分组 + 统计描述
   - 仪表板 → 按图表区域分割

3. **混合处理**：
   - 自动识别文档类型
   - 选择最佳处理策略
   - 统一输出格式

这样的方案能够很好地处理企业中常见的PDF和Excel文档！
